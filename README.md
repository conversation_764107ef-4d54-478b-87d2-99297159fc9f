# 小科狗输入法官方网站

这是小科狗输入法的官方网站项目，使用纯HTML、CSS和JavaScript开发的静态网站。

## 项目特点

- 🚀 **纯静态网站** - 使用原生HTML/CSS/JS，无框架依赖
- 📱 **响应式设计** - 完美支持桌面端和移动端
- ⚡ **性能优化** - 轻量级设计，加载速度快
- 🎨 **现代设计** - 简洁美观的用户界面
- 🔧 **易于维护** - 代码结构清晰，便于修改和扩展

## 网站结构

```
xiaokegou/
├── index.html              # 主页面
├── tutorial.html           # 教程页面
├── css/
│   └── style.css          # 样式文件
├── js/
│   └── script.js          # JavaScript功能
├── images/                # 图片资源
│   ├── README.md          # 图片说明文档
│   └── favicon-generator.html  # 图标生成工具
├── README.md              # 项目说明
└── deploy.md              # 部署指南
```

## 网站功能

### 主要页面
- **首页 (index.html)**
  - Hero区域 - 产品介绍和核心价值展示
  - 产品特色 - 6大核心功能亮点
  - 下载安装 - 软件下载和安装说明
  - 使用教程 - 教程入口和视频链接
  - 常见问题 - FAQ折叠式问答
  - 社区生态 - QQ群、B站、头条等社区入口
  - 关于我们 - 产品理念和开发者信息

- **教程页面 (tutorial.html)**
  - 安装指南 - 详细的安装步骤
  - 基础使用 - 输入法基本操作
  - 高级功能 - 编码方案、词库管理等
  - 使用技巧 - 快捷键和优化建议
  - 问题解决 - 常见问题的解决方案
  - 视频教程 - B站教程视频链接

### 交互功能
- 响应式导航菜单（移动端汉堡菜单）
- 平滑滚动导航
- FAQ折叠展开
- QQ群号一键复制
- 通知提示系统
- 滚动动画效果

## 技术特性

### CSS特性
- CSS Grid 和 Flexbox 布局
- CSS 变量和渐变
- 响应式媒体查询
- 平滑过渡动画
- 毛玻璃效果（backdrop-filter）

### JavaScript特性
- 模块化函数设计
- 事件委托和防抖节流
- 交叉观察器API（Intersection Observer）
- 剪贴板API
- 页面可见性API

## 浏览器支持

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- 移动端浏览器

## 部署说明

### 本地预览
直接用浏览器打开 `index.html` 文件即可预览。

### 生产部署
1. 将整个项目文件夹上传到Web服务器
2. 确保服务器支持静态文件访问
3. 配置域名指向项目根目录

### 推荐的Web服务器
- Nginx
- Apache
- GitHub Pages
- Netlify
- Vercel

## 自定义配置

### 修改网站信息
编辑 `index.html` 文件中的以下内容：
- 网站标题和描述
- 联系方式和QQ群号
- 下载链接
- 社交媒体链接

### 修改样式
编辑 `css/style.css` 文件：
- 主题颜色变量
- 字体设置
- 布局参数
- 动画效果

### 添加功能
编辑 `js/script.js` 文件：
- 新增交互功能
- 修改现有行为
- 添加数据统计

## 图片资源

网站需要以下图片资源（位于 `images/` 文件夹）：
- `logo.png` - 网站Logo (80x80px)
- `favicon.ico` - 网站图标
- `hero-screenshot.png` - 首页展示图
- `about-illustration.png` - 关于页面插图

详细说明请查看 `images/README.md`

## 性能优化建议

1. **图片优化**
   - 使用WebP格式
   - 压缩图片大小
   - 实现懒加载

2. **代码优化**
   - 压缩CSS和JS文件
   - 启用Gzip压缩
   - 使用CDN加速

3. **缓存策略**
   - 设置适当的缓存头
   - 使用版本号管理静态资源

## 更新日志

### v1.0.0 (2024-05-29)
- 初始版本发布
- 完整的响应式网站
- 所有核心功能实现
- FAQ系统
- 社区链接集成

## 联系方式

- QQ群1: 641858743
- QQ群2: 498060191  
- QQ群3: 641389627
- 网站: xiaohegou.com

## 开源协议

本项目采用 MIT 协议开源。

---

**Keep Earth Green - 让输入更美好**
