<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>使用教程 - 小科狗输入法</title>
    <meta name="description" content="小科狗输入法详细使用教程，包括安装、配置、使用技巧等完整指南。">
    <meta name="keywords" content="小科狗输入法,使用教程,安装指南,配置说明,KEG输入法">
    <link rel="stylesheet" href="css/style.css">
    <link rel="icon" href="images/favicon.ico" type="image/x-icon">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="images/logo.png" alt="小科狗输入法" class="logo-img">
                <span class="logo-text">小科狗输入法</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html">首页</a></li>
                <li><a href="index.html#features">特色功能</a></li>
                <li><a href="index.html#download">下载</a></li>
                <li><a href="tutorial.html" class="active">教程</a></li>
                <li><a href="index.html#community">社区</a></li>
                <li><a href="index.html#about">关于</a></li>
            </ul>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 教程页面头部 -->
    <section class="tutorial-hero">
        <div class="container">
            <h1>小科狗输入法使用教程</h1>
            <p class="tutorial-subtitle">从安装到精通，全面掌握小科狗输入法的使用方法</p>
            <nav class="tutorial-nav">
                <a href="#install" class="tutorial-nav-link">安装指南</a>
                <a href="#basic" class="tutorial-nav-link">基础使用</a>
                <a href="#advanced" class="tutorial-nav-link">高级功能</a>
                <a href="#tips" class="tutorial-nav-link">使用技巧</a>
                <a href="#troubleshooting" class="tutorial-nav-link">问题解决</a>
            </nav>
        </div>
    </section>

    <!-- 安装指南 -->
    <section id="install" class="tutorial-section">
        <div class="container">
            <h2 class="section-title">安装指南</h2>

            <div class="tutorial-content">
                <div class="install-intro">
                    <h3>小科狗输入法平台简介</h3>
                    <p>小科狗输入法平台真名为KEG输入法（取Keep Earth Green首字母组合），是一款个人开发的永远免费的输入法平台。KEG平台的目标是打造一款挂码方便、码字爽快和打遍天下所有编码方案的输入法平台。</p>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>系统要求</h3>
                        <p>在安装前，请确认您的系统满足以下要求：</p>
                        <ul class="step-list">
                            <li>操作系统：Windows 7/8/10/11（32位或64位）</li>
                            <li>内存：至少512MB可用内存</li>
                            <li>硬盘空间：至少50MB可用空间</li>
                            <li>管理员权限：安装时需要管理员权限</li>
                        </ul>
                    </div>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>下载安装包</h3>
                        <p>从官网下载最新版本的小科狗输入法安装包。安装包通常包含以下文件：</p>
                        <ul class="step-list">
                            <li><strong>KEG.exe</strong> - 主安装程序</li>
                            <li><strong>Keg.db</strong> - 方案数据库文件</li>
                            <li><strong>keg.txt</strong> - 配置文件</li>
                            <li><strong>皮肤文件夹</strong> - 包含各种皮肤主题</li>
                        </ul>
                        <div class="step-image">
                            <img src="images/install-files.png" alt="安装文件列表">
                        </div>
                    </div>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>运行安装程序</h3>
                        <p>双击KEG.exe安装程序，按照以下步骤进行安装：</p>
                        <ul class="step-list">
                            <li>右键点击KEG.exe，选择"以管理员身份运行"</li>
                            <li>如果出现Windows安全提示，点击"是"继续</li>
                            <li>选择安装路径（建议使用默认路径C:\Program Files\KEG）</li>
                            <li>选择要安装的组件（建议全部安装）</li>
                            <li>点击"安装"开始安装过程</li>
                        </ul>
                        <div class="step-image">
                            <img src="images/install-wizard.png" alt="安装向导">
                        </div>
                    </div>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3>完成安装</h3>
                        <p>安装完成后，需要进行以下操作：</p>
                        <ul class="step-list">
                            <li>重启计算机或注销重新登录</li>
                            <li>在任务栏右下角找到输入法图标</li>
                            <li>右键点击输入法图标，确认KEG输入法已添加</li>
                            <li>使用Ctrl+Shift切换到KEG输入法</li>
                        </ul>
                        <div class="step-note">
                            <strong>重要提示：</strong>安装完成后必须重启系统，否则输入法可能无法正常工作。如果重启后仍无法使用，请检查输入法是否已正确添加到系统中。
                        </div>
                    </div>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <h3>验证安装</h3>
                        <p>安装完成后，可以通过以下方式验证安装是否成功：</p>
                        <ul class="step-list">
                            <li>打开记事本或任意文本编辑器</li>
                            <li>按Ctrl+Shift切换输入法，直到出现KEG图标</li>
                            <li>尝试输入一些文字，检查是否能正常显示候选词</li>
                            <li>右键点击输入法状态栏，查看是否有设置选项</li>
                        </ul>
                        <div class="step-image">
                            <img src="images/install-verify.png" alt="验证安装">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 基础使用 -->
    <section id="basic" class="tutorial-section">
        <div class="container">
            <h2 class="section-title">基础使用</h2>

            <div class="tutorial-content">
                <div class="basic-intro">
                    <h3>开始使用KEG输入法</h3>
                    <p>KEG输入法安装完成后，您就可以开始使用了。KEG支持多种输入方案，包括拼音、五笔、双拼等，可以满足不同用户的需求。</p>
                </div>

                <div class="tutorial-card">
                    <h3>🔄 切换输入法</h3>
                    <p>使用快捷键快速切换到小科狗输入法：</p>
                    <div class="shortcut-box">
                        <kbd>Ctrl</kbd> + <kbd>Shift</kbd> 切换输入法
                    </div>
                    <div class="shortcut-box">
                        <kbd>Ctrl</kbd> + <kbd>Space</kbd> 中英文切换
                    </div>
                    <div class="shortcut-box">
                        <kbd>Shift</kbd> + <kbd>Space</kbd> 全角半角切换
                    </div>
                    <p><strong>提示：</strong>首次使用时，请确保在任务栏右下角能看到KEG输入法的图标。如果没有，请检查输入法是否正确安装。</p>
                </div>

                <div class="tutorial-card">
                    <h3>⌨️ 基本输入操作</h3>
                    <p>KEG输入法的基本操作方式：</p>
                    <ul>
                        <li><strong>拼音输入：</strong>直接输入拼音字母，系统会自动显示候选词</li>
                        <li><strong>五笔输入：</strong>输入五笔编码，支持词组和单字输入</li>
                        <li><strong>混合输入：</strong>可以在拼音和五笔之间快速切换</li>
                        <li><strong>英文输入：</strong>按Ctrl+Space切换到英文模式</li>
                    </ul>
                    <div class="input-example">
                        <h4>输入示例：</h4>
                        <div class="example-item">
                            <span class="input-code">输入：xiaokegou</span>
                            <span class="arrow">→</span>
                            <span class="output-text">输出：小科狗</span>
                        </div>
                        <div class="example-item">
                            <span class="input-code">输入：srf</span>
                            <span class="arrow">→</span>
                            <span class="output-text">输出：输入法</span>
                        </div>
                    </div>
                </div>

                <div class="tutorial-card">
                    <h3>🎯 候选词选择</h3>
                    <p>KEG提供多种候选词选择方式，提高输入效率：</p>
                    <div class="candidate-demo">
                        <img src="images/candidate-selection.png" alt="候选词选择示例">
                    </div>
                    <div class="selection-methods">
                        <h4>选择方法：</h4>
                        <ul>
                            <li><strong>数字键 1-9：</strong>选择对应位置的候选词</li>
                            <li><strong>空格键：</strong>选择第一个候选词（最常用）</li>
                            <li><strong>Tab键：</strong>选择第二个候选词</li>
                            <li><strong>回车键：</strong>确认当前输入的编码</li>
                            <li><strong>方向键：</strong>上下翻页，左右移动光标</li>
                            <li><strong>Page Up/Down：</strong>快速翻页</li>
                        </ul>
                    </div>
                </div>

                <div class="tutorial-card">
                    <h3>📝 输入技巧</h3>
                    <div class="tips-grid-basic">
                        <div class="tip-item">
                            <h4>🚀 快速输入</h4>
                            <ul>
                                <li>使用简拼：输入声母快速匹配</li>
                                <li>词组输入：输入多个字的首字母</li>
                                <li>自动记忆：常用词会自动提升优先级</li>
                            </ul>
                        </div>
                        <div class="tip-item">
                            <h4>🔧 编辑功能</h4>
                            <ul>
                                <li>Backspace：删除前一个字符</li>
                                <li>Delete：删除后一个字符</li>
                                <li>Ctrl+Backspace：删除整个词组</li>
                            </ul>
                        </div>
                        <div class="tip-item">
                            <h4>📋 特殊输入</h4>
                            <ul>
                                <li>标点符号：直接输入或使用快捷键</li>
                                <li>数字输入：支持中文数字转换</li>
                                <li>日期时间：支持快速日期输入</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="tutorial-card">
                    <h3>⚙️ 状态栏功能</h3>
                    <p>KEG输入法状态栏提供了丰富的功能选项：</p>
                    <div class="statusbar-demo">
                        <img src="images/statusbar-demo.png" alt="状态栏功能">
                    </div>
                    <div class="statusbar-functions">
                        <h4>操作方式：</h4>
                        <ul>
                            <li><strong>左键点击：</strong>显示/隐藏候选词窗口</li>
                            <li><strong>右键点击：</strong>打开功能菜单</li>
                            <li><strong>双击：</strong>快速切换输入方案</li>
                            <li><strong>拖拽：</strong>移动状态栏位置</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 高级功能 -->
    <section id="advanced" class="tutorial-section">
        <div class="container">
            <h2 class="section-title">高级功能</h2>

            <div class="tutorial-content">
                <div class="advanced-intro">
                    <h3>深度定制KEG输入法</h3>
                    <p>KEG输入法提供了丰富的高级功能，让您可以根据个人习惯深度定制输入体验。这些功能包括方案管理、词库定制、皮肤设置等。</p>
                </div>

                <div class="feature-item-large">
                    <h3>📚 输入方案管理</h3>
                    <p>KEG最大的特色是支持多种输入方案，可以满足不同用户的需求：</p>

                    <div class="scheme-details">
                        <h4>内置方案：</h4>
                        <div class="scheme-grid">
                            <div class="scheme-card">
                                <h5>🔤 现代五笔</h5>
                                <p>基于五笔字型的现代化改进版本，支持词组输入和智能联想。</p>
                                <div class="scheme-features">
                                    <span class="feature-tag">词组输入</span>
                                    <span class="feature-tag">智能联想</span>
                                    <span class="feature-tag">简码优化</span>
                                </div>
                            </div>
                            <div class="scheme-card">
                                <h5>🎯 拼音方案</h5>
                                <p>支持全拼、简拼、模糊音等多种拼音输入方式。</p>
                                <div class="scheme-features">
                                    <span class="feature-tag">全拼输入</span>
                                    <span class="feature-tag">简拼支持</span>
                                    <span class="feature-tag">模糊音</span>
                                </div>
                            </div>
                            <div class="scheme-card">
                                <h5>⚡ 双拼方案</h5>
                                <p>支持多种双拼方案，提高拼音输入效率。</p>
                                <div class="scheme-features">
                                    <span class="feature-tag">小鹤双拼</span>
                                    <span class="feature-tag">自然码</span>
                                    <span class="feature-tag">微软双拼</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="how-to-detailed">
                        <h4>方案切换方法：</h4>
                        <ol>
                            <li>右键点击KEG输入法状态栏</li>
                            <li>选择"输入方案"菜单</li>
                            <li>从列表中选择需要的方案</li>
                            <li>或使用快捷键Ctrl+`快速切换</li>
                        </ol>
                        <div class="scheme-image">
                            <img src="images/scheme-switch.png" alt="方案切换界面">
                        </div>
                    </div>
                </div>

                <div class="feature-item-large">
                    <h3>📖 词库管理系统</h3>
                    <p>KEG提供强大的词库管理功能，支持导入、编辑、备份词库：</p>

                    <div class="dict-management">
                        <h4>词库文件说明：</h4>
                        <ul>
                            <li><strong>Keg.db：</strong>主词库文件，包含所有输入方案的词汇数据</li>
                            <li><strong>用户词库：</strong>存储个人添加的词汇和使用习惯</li>
                            <li><strong>专业词库：</strong>可导入医学、法律、计算机等专业词库</li>
                        </ul>

                        <h4>词库操作：</h4>
                        <div class="dict-operations">
                            <div class="operation-item">
                                <h5>📥 导入词库</h5>
                                <ol>
                                    <li>准备词库文件（支持.txt、.dict等格式）</li>
                                    <li>右键状态栏 → 设置 → 词库管理</li>
                                    <li>点击"导入词库"按钮</li>
                                    <li>选择词库文件并确认导入</li>
                                </ol>
                            </div>
                            <div class="operation-item">
                                <h5>✏️ 编辑词库</h5>
                                <ol>
                                    <li>打开词库管理界面</li>
                                    <li>选择要编辑的词库</li>
                                    <li>添加、删除或修改词条</li>
                                    <li>保存更改并重新加载</li>
                                </ol>
                            </div>
                            <div class="operation-item">
                                <h5>💾 备份词库</h5>
                                <ol>
                                    <li>定期备份Keg.db文件</li>
                                    <li>导出个人词库设置</li>
                                    <li>保存到安全位置</li>
                                    <li>需要时可快速恢复</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="feature-item-large">
                    <h3>🎨 皮肤与界面定制</h3>
                    <p>KEG支持静态和动态皮肤，让您的输入法界面更加个性化：</p>

                    <div class="skin-system">
                        <h4>皮肤类型：</h4>
                        <div class="skin-types">
                            <div class="skin-type">
                                <h5>🖼️ 静态皮肤</h5>
                                <p>使用固定的背景图片和颜色方案</p>
                                <ul>
                                    <li>支持PNG、JPG格式</li>
                                    <li>可自定义字体颜色</li>
                                    <li>支持透明效果</li>
                                </ul>
                            </div>
                            <div class="skin-type">
                                <h5>🎬 动态皮肤</h5>
                                <p>支持动画效果和交互反馈</p>
                                <ul>
                                    <li>动画背景效果</li>
                                    <li>按键反馈动画</li>
                                    <li>候选词高亮效果</li>
                                </ul>
                            </div>
                        </div>

                        <h4>皮肤配置：</h4>
                        <p>皮肤配置文件位于安装目录下的keg.txt文件中，您可以手动编辑或使用图形界面设置：</p>
                        <div class="config-example">
                            <h5>配置示例：</h5>
                            <pre><code>[Skin]
BackgroundImage=skin/default.png
FontColor=#000000
CandidateColor=#333333
HighlightColor=#0078d4
Transparency=80</code></pre>
                        </div>
                    </div>
                </div>

                <div class="feature-grid">
                    <div class="feature-item">
                        <h3>⚡ 智能功能</h3>
                        <ul>
                            <li><strong>智能联想：</strong>根据上下文推荐词汇</li>
                            <li><strong>自动纠错：</strong>自动修正常见输入错误</li>
                            <li><strong>词频调整：</strong>常用词自动提升优先级</li>
                            <li><strong>云同步：</strong>支持词库云端同步（需配置）</li>
                        </ul>
                    </div>

                    <div class="feature-item">
                        <h3>🔧 高级设置</h3>
                        <ul>
                            <li><strong>候选词数量：</strong>可设置显示1-9个候选词</li>
                            <li><strong>翻页键设置：</strong>自定义翻页快捷键</li>
                            <li><strong>标点符号：</strong>中英文标点自动转换</li>
                            <li><strong>快捷短语：</strong>设置常用短语快捷输入</li>
                        </ul>
                    </div>

                    <div class="feature-item">
                        <h3>📊 统计功能</h3>
                        <ul>
                            <li><strong>输入统计：</strong>查看输入字数和效率</li>
                            <li><strong>词频统计：</strong>分析常用词汇</li>
                            <li><strong>错误统计：</strong>记录常见输入错误</li>
                            <li><strong>使用报告：</strong>生成详细使用报告</li>
                        </ul>
                    </div>

                    <div class="feature-item">
                        <h3>🔒 安全设置</h3>
                        <ul>
                            <li><strong>隐私保护：</strong>敏感词汇不记录</li>
                            <li><strong>安全模式：</strong>在密码框中禁用记忆</li>
                            <li><strong>数据加密：</strong>用户数据本地加密存储</li>
                            <li><strong>清理功能：</strong>定期清理输入历史</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 使用技巧 -->
    <section id="tips" class="tutorial-section">
        <div class="container">
            <h2 class="section-title">使用技巧</h2>
            
            <div class="tips-grid">
                <div class="tip-card">
                    <div class="tip-icon">💡</div>
                    <h3>快速输入技巧</h3>
                    <ul>
                        <li>使用简拼：输入声母快速匹配词汇</li>
                        <li>利用联想：输入部分内容自动联想</li>
                        <li>记忆功能：常用词汇会自动提升优先级</li>
                        <li>模糊音：支持 z/zh、c/ch、s/sh 等模糊输入</li>
                    </ul>
                </div>

                <div class="tip-card">
                    <div class="tip-icon">⌨️</div>
                    <h3>快捷键大全</h3>
                    <ul>
                        <li><strong><kbd>Ctrl</kbd>+<kbd>Shift</kbd>：</strong>切换输入法</li>
                        <li><strong><kbd>Ctrl</kbd>+<kbd>Space</kbd>：</strong>中英文切换</li>
                        <li><strong><kbd>Shift</kbd>+<kbd>Space</kbd>：</strong>全角半角切换</li>
                        <li><strong><kbd>Ctrl</kbd>+<kbd>.</kbd>：</strong>中英文标点切换</li>
                    </ul>
                </div>

                <div class="tip-card">
                    <div class="tip-icon">🔧</div>
                    <h3>性能优化</h3>
                    <ul>
                        <li>定期清理输入历史</li>
                        <li>关闭不需要的功能</li>
                        <li>调整候选词数量</li>
                        <li>优化词库大小</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- 问题解决 -->
    <section id="troubleshooting" class="tutorial-section">
        <div class="container">
            <h2 class="section-title">常见问题解决</h2>
            
            <div class="troubleshooting-content">
                <div class="problem-item">
                    <h3>❌ 输入法无法切换</h3>
                    <div class="solution">
                        <h4>解决方案：</h4>
                        <ol>
                            <li>检查输入法是否正确安装</li>
                            <li>重启输入法服务：任务管理器 → 结束输入法进程 → 重新启动</li>
                            <li>检查快捷键设置是否冲突</li>
                            <li>以管理员身份重新安装</li>
                        </ol>
                    </div>
                </div>

                <div class="problem-item">
                    <h3>🐌 输入法响应缓慢</h3>
                    <div class="solution">
                        <h4>解决方案：</h4>
                        <ol>
                            <li>关闭不必要的后台程序</li>
                            <li>清理输入法缓存</li>
                            <li>减少候选词数量</li>
                            <li>关闭复杂的皮肤主题</li>
                        </ol>
                    </div>
                </div>

                <div class="problem-item">
                    <h3>📝 候选词不准确</h3>
                    <div class="solution">
                        <h4>解决方案：</h4>
                        <ol>
                            <li>更新词库到最新版本</li>
                            <li>手动添加常用词汇</li>
                            <li>调整词频统计</li>
                            <li>重置用户词库</li>
                        </ol>
                    </div>
                </div>

                <div class="problem-item">
                    <h3>🔧 配置文件问题</h3>
                    <div class="solution">
                        <h4>解决方案：</h4>
                        <ol>
                            <li>检查keg.txt配置文件格式是否正确</li>
                            <li>确保配置文件编码为UTF-8</li>
                            <li>备份后重置为默认配置</li>
                            <li>参考官方配置文档进行修改</li>
                        </ol>
                    </div>
                </div>

                <div class="problem-item">
                    <h3>💾 词库损坏</h3>
                    <div class="solution">
                        <h4>解决方案：</h4>
                        <ol>
                            <li>从备份恢复Keg.db文件</li>
                            <li>重新下载官方词库文件</li>
                            <li>使用词库修复工具（如有）</li>
                            <li>重新安装输入法</li>
                        </ol>
                    </div>
                </div>

                <div class="problem-item">
                    <h3>🎨 皮肤显示异常</h3>
                    <div class="solution">
                        <h4>解决方案：</h4>
                        <ol>
                            <li>检查皮肤文件是否完整</li>
                            <li>确认图片格式支持（PNG/JPG）</li>
                            <li>重置为默认皮肤</li>
                            <li>更新显卡驱动程序</li>
                        </ol>
                    </div>
                </div>

                <div class="troubleshooting-tips">
                    <h3>💡 故障排除技巧</h3>
                    <div class="tips-container">
                        <div class="troubleshooting-tip">
                            <h4>🔍 日志查看</h4>
                            <p>KEG输入法会在安装目录生成日志文件，可以通过查看日志来诊断问题：</p>
                            <ul>
                                <li>错误日志：error.log</li>
                                <li>运行日志：runtime.log</li>
                                <li>配置日志：config.log</li>
                            </ul>
                        </div>

                        <div class="troubleshooting-tip">
                            <h4>🛠️ 重置方法</h4>
                            <p>如果遇到严重问题，可以尝试重置输入法：</p>
                            <ul>
                                <li>删除用户配置文件</li>
                                <li>清空输入历史</li>
                                <li>恢复默认设置</li>
                                <li>重新启动输入法服务</li>
                            </ul>
                        </div>

                        <div class="troubleshooting-tip">
                            <h4>📞 获取帮助</h4>
                            <p>如果问题仍未解决，可以通过以下方式获取帮助：</p>
                            <ul>
                                <li>加入官方QQ群：641858743</li>
                                <li>查看B站视频教程</li>
                                <li>访问官方网站FAQ</li>
                                <li>联系开发者反馈问题</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 视频教程区域 -->
    <section class="video-tutorial">
        <div class="container">
            <h2 class="section-title">视频教程</h2>
            <div class="video-grid">
                <div class="video-card">
                    <div class="video-thumbnail">
                        <img src="images/video-thumb-1.jpg" alt="安装教程视频">
                        <div class="play-button">▶</div>
                    </div>
                    <h3>安装与基础设置</h3>
                    <p>详细演示小科狗输入法的安装过程和基础配置</p>
                    <a href="https://www.bilibili.com/video/BV1rrPAejEEv/" target="_blank" class="btn btn-outline">观看视频</a>
                </div>
                
                <div class="video-card">
                    <div class="video-thumbnail">
                        <img src="images/video-thumb-2.jpg" alt="高级功能视频">
                        <div class="play-button">▶</div>
                    </div>
                    <h3>高级功能使用</h3>
                    <p>介绍编码方案切换、词库管理等高级功能</p>
                    <a href="https://www.bilibili.com/video/BV1rrPAejEEv/" target="_blank" class="btn btn-outline">观看视频</a>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>小科狗输入法</h4>
                    <p>永远免费的输入法平台</p>
                    <div class="footer-social">
                        <a href="#" title="QQ群">💬</a>
                        <a href="https://www.bilibili.com/video/BV1rrPAejEEv/" target="_blank" title="B站">📺</a>
                        <a href="https://www.toutiao.com/c/user/token/MS4wLjABAAAAsItj-RKKszxsyp_4Y_D82QpBTYN4X13J_rt3giogb1B1ZEiJDQZTJAQLw_ZJv1SP" target="_blank" title="头条">📰</a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="index.html#download">下载软件</a></li>
                        <li><a href="tutorial.html">使用手册</a></li>
                        <li><a href="index.html#tutorial">视频教程</a></li>
                        <li><a href="index.html#community">加入社区</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <ul>
                        <li>QQ群1: 641858743</li>
                        <li>QQ群2: 498060191</li>
                        <li>QQ群3: 641389627</li>
                        <li>网站: xiaokegou.com</li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>支持我们</h4>
                    <p>如果您觉得小科狗输入法好用，欢迎打赏支持开发者继续改进产品。</p>
                    <button class="btn btn-donate">❤️ 打赏支持</button>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 小科狗输入法. 保留所有权利. | 网站: xiaokegou.com</p>
                <p>Keep Earth Green - 让输入更美好</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
</body>
</html>
