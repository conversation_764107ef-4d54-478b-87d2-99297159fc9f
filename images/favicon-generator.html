<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Favicon 生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .generator {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        canvas {
            border: 1px solid #ddd;
            margin: 20px 0;
        }
        button {
            background: #2c5aa0;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1e3f73;
        }
        .preview {
            display: flex;
            gap: 20px;
            align-items: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="generator">
        <h1>小科狗输入法 Favicon 生成器</h1>
        <p>这个工具可以帮助您生成网站所需的favicon图标。</p>
        
        <canvas id="canvas" width="64" height="64"></canvas>
        
        <div>
            <button onclick="generateFavicon('KEG')">生成 KEG 图标</button>
            <button onclick="generateFavicon('狗')">生成 狗 图标</button>
            <button onclick="generateFavicon('K')">生成 K 图标</button>
        </div>
        
        <div class="preview">
            <span>预览效果：</span>
            <img id="preview16" width="16" height="16" style="border: 1px solid #ddd;">
            <img id="preview32" width="32" height="32" style="border: 1px solid #ddd;">
        </div>
        
        <button onclick="downloadFavicon()">下载 Favicon</button>
        
        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px;">
            <h3>使用说明：</h3>
            <ol>
                <li>点击上方按钮生成不同样式的图标</li>
                <li>查看预览效果</li>
                <li>点击"下载 Favicon"保存为 favicon.ico</li>
                <li>将生成的文件放入网站的 images 文件夹</li>
            </ol>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        function generateFavicon(text) {
            // 清空画布
            ctx.clearRect(0, 0, 64, 64);
            
            // 绘制背景
            const gradient = ctx.createLinearGradient(0, 0, 64, 64);
            gradient.addColorStop(0, '#2c5aa0');
            gradient.addColorStop(1, '#1e3f73');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 64, 64);
            
            // 绘制文字
            ctx.fillStyle = 'white';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, 32, 32);
            
            // 更新预览
            updatePreview();
        }
        
        function updatePreview() {
            const dataURL = canvas.toDataURL();
            document.getElementById('preview16').src = dataURL;
            document.getElementById('preview32').src = dataURL;
        }
        
        function downloadFavicon() {
            // 创建一个临时链接来下载图片
            const link = document.createElement('a');
            link.download = 'favicon.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 初始化生成一个默认图标
        generateFavicon('KEG');
    </script>
</body>
</html>
