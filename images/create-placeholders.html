<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片占位符生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .generator {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        canvas {
            border: 1px solid #ddd;
            margin: 20px 0;
            display: block;
        }
        button {
            background: #2c5aa0;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1e3f73;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .image-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .image-item h3 {
            color: #2c5aa0;
            margin-bottom: 15px;
        }
        .download-all {
            background: #059669;
            font-size: 1.1rem;
            padding: 15px 30px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="generator">
        <h1>小科狗输入法图片占位符生成器</h1>
        <p>这个工具可以帮助您生成网站所需的占位符图片。</p>
        
        <button onclick="generateAllImages()" class="download-all">生成所有图片</button>
        
        <div class="image-grid">
            <div class="image-item">
                <h3>Logo (80x80)</h3>
                <canvas id="logo" width="80" height="80"></canvas>
                <button onclick="downloadImage('logo', 'logo.png')">下载</button>
            </div>
            
            <div class="image-item">
                <h3>安装文件 (600x300)</h3>
                <canvas id="install-files" width="600" height="300"></canvas>
                <button onclick="downloadImage('install-files', 'install-files.png')">下载</button>
            </div>
            
            <div class="image-item">
                <h3>安装向导 (600x400)</h3>
                <canvas id="install-wizard" width="600" height="400"></canvas>
                <button onclick="downloadImage('install-wizard', 'install-wizard.png')">下载</button>
            </div>
            
            <div class="image-item">
                <h3>安装验证 (500x300)</h3>
                <canvas id="install-verify" width="500" height="300"></canvas>
                <button onclick="downloadImage('install-verify', 'install-verify.png')">下载</button>
            </div>
            
            <div class="image-item">
                <h3>候选词选择 (500x200)</h3>
                <canvas id="candidate-selection" width="500" height="200"></canvas>
                <button onclick="downloadImage('candidate-selection', 'candidate-selection.png')">下载</button>
            </div>
            
            <div class="image-item">
                <h3>状态栏演示 (400x150)</h3>
                <canvas id="statusbar-demo" width="400" height="150"></canvas>
                <button onclick="downloadImage('statusbar-demo', 'statusbar-demo.png')">下载</button>
            </div>
            
            <div class="image-item">
                <h3>方案切换 (500x350)</h3>
                <canvas id="scheme-switch" width="500" height="350"></canvas>
                <button onclick="downloadImage('scheme-switch', 'scheme-switch.png')">下载</button>
            </div>
            
            <div class="image-item">
                <h3>视频缩略图1 (400x225)</h3>
                <canvas id="video-thumb-1" width="400" height="225"></canvas>
                <button onclick="downloadImage('video-thumb-1', 'video-thumb-1.jpg')">下载</button>
            </div>
            
            <div class="image-item">
                <h3>视频缩略图2 (400x225)</h3>
                <canvas id="video-thumb-2" width="400" height="225"></canvas>
                <button onclick="downloadImage('video-thumb-2', 'video-thumb-2.jpg')">下载</button>
            </div>
        </div>
    </div>

    <script>
        function createGradient(ctx, width, height, color1, color2) {
            const gradient = ctx.createLinearGradient(0, 0, width, height);
            gradient.addColorStop(0, color1);
            gradient.addColorStop(1, color2);
            return gradient;
        }
        
        function drawLogo() {
            const canvas = document.getElementById('logo');
            const ctx = canvas.getContext('2d');
            
            // 背景渐变
            ctx.fillStyle = createGradient(ctx, 80, 80, '#2c5aa0', '#1e3f73');
            ctx.fillRect(0, 0, 80, 80);
            
            // 文字
            ctx.fillStyle = 'white';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('KEG', 40, 40);
        }
        
        function drawInstallFiles() {
            const canvas = document.getElementById('install-files');
            const ctx = canvas.getContext('2d');
            
            // 背景
            ctx.fillStyle = '#f8fafc';
            ctx.fillRect(0, 0, 600, 300);
            
            // 边框
            ctx.strokeStyle = '#e2e8f0';
            ctx.lineWidth = 2;
            ctx.strokeRect(0, 0, 600, 300);
            
            // 标题
            ctx.fillStyle = '#2c5aa0';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('KEG 安装文件', 300, 50);
            
            // 文件列表
            const files = ['KEG.exe', 'Keg.db', 'keg.txt', '皮肤文件夹'];
            ctx.fillStyle = '#4a5568';
            ctx.font = '16px Arial';
            ctx.textAlign = 'left';
            
            files.forEach((file, index) => {
                const y = 120 + index * 30;
                ctx.fillText('📁 ' + file, 100, y);
            });
        }
        
        function drawInstallWizard() {
            const canvas = document.getElementById('install-wizard');
            const ctx = canvas.getContext('2d');
            
            // 背景
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, 600, 400);
            
            // 边框
            ctx.strokeStyle = '#d1d5db';
            ctx.lineWidth = 1;
            ctx.strokeRect(0, 0, 600, 400);
            
            // 标题栏
            ctx.fillStyle = '#2c5aa0';
            ctx.fillRect(0, 0, 600, 60);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('KEG 输入法安装向导', 300, 35);
            
            // 内容区域
            ctx.fillStyle = '#374151';
            ctx.font = '16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('欢迎使用 KEG 输入法安装程序', 50, 120);
            ctx.fillText('请选择安装路径：', 50, 160);
            
            // 路径框
            ctx.strokeStyle = '#d1d5db';
            ctx.strokeRect(50, 180, 400, 30);
            ctx.fillStyle = '#f9fafb';
            ctx.fillRect(51, 181, 398, 28);
            ctx.fillStyle = '#6b7280';
            ctx.font = '14px Arial';
            ctx.fillText('C:\\Program Files\\KEG', 60, 200);
            
            // 按钮
            ctx.fillStyle = '#2c5aa0';
            ctx.fillRect(400, 320, 80, 35);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('安装', 440, 342);
        }
        
        function drawInstallVerify() {
            const canvas = document.getElementById('install-verify');
            const ctx = canvas.getContext('2d');
            
            // 背景
            ctx.fillStyle = '#f0f9ff';
            ctx.fillRect(0, 0, 500, 300);
            
            // 边框
            ctx.strokeStyle = '#2c5aa0';
            ctx.lineWidth = 2;
            ctx.strokeRect(0, 0, 500, 300);
            
            // 成功图标
            ctx.fillStyle = '#10b981';
            ctx.font = '48px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('✓', 250, 80);
            
            // 文字
            ctx.fillStyle = '#2c5aa0';
            ctx.font = 'bold 20px Arial';
            ctx.fillText('安装成功！', 250, 130);
            
            ctx.fillStyle = '#6b7280';
            ctx.font = '16px Arial';
            ctx.fillText('KEG 输入法已成功安装到您的系统', 250, 170);
            ctx.fillText('请重启计算机以完成安装', 250, 200);
        }
        
        function drawCandidateSelection() {
            const canvas = document.getElementById('candidate-selection');
            const ctx = canvas.getContext('2d');
            
            // 背景
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, 500, 200);
            
            // 边框
            ctx.strokeStyle = '#d1d5db';
            ctx.lineWidth = 1;
            ctx.strokeRect(0, 0, 500, 200);
            
            // 输入框
            ctx.fillStyle = '#f9fafb';
            ctx.fillRect(20, 30, 460, 40);
            ctx.strokeStyle = '#2c5aa0';
            ctx.lineWidth = 2;
            ctx.strokeRect(20, 30, 460, 40);
            
            ctx.fillStyle = '#374151';
            ctx.font = '18px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('xiaokegou', 30, 55);
            
            // 候选词
            const candidates = ['1.小科狗', '2.小可狗', '3.小科够', '4.小科购'];
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(20, 90, 460, 80);
            ctx.strokeStyle = '#d1d5db';
            ctx.strokeRect(20, 90, 460, 80);
            
            candidates.forEach((candidate, index) => {
                const x = 30 + index * 110;
                if (index === 0) {
                    ctx.fillStyle = '#2c5aa0';
                    ctx.fillRect(x - 5, 100, 100, 25);
                    ctx.fillStyle = 'white';
                } else {
                    ctx.fillStyle = '#374151';
                }
                ctx.font = '14px Arial';
                ctx.fillText(candidate, x, 118);
            });
        }
        
        function drawStatusbarDemo() {
            const canvas = document.getElementById('statusbar-demo');
            const ctx = canvas.getContext('2d');
            
            // 背景
            ctx.fillStyle = '#f8fafc';
            ctx.fillRect(0, 0, 400, 150);
            
            // 状态栏
            ctx.fillStyle = '#2c5aa0';
            ctx.fillRect(50, 50, 300, 50);
            
            // 图标
            ctx.fillStyle = 'white';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('KEG', 80, 80);
            
            // 状态文字
            ctx.font = '16px Arial';
            ctx.fillText('中文', 150, 80);
            ctx.fillText('五笔', 220, 80);
            ctx.fillText('简体', 290, 80);
            
            // 说明文字
            ctx.fillStyle = '#6b7280';
            ctx.font = '12px Arial';
            ctx.fillText('右键点击打开菜单', 200, 130);
        }
        
        function drawSchemeSwitch() {
            const canvas = document.getElementById('scheme-switch');
            const ctx = canvas.getContext('2d');
            
            // 背景
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, 500, 350);
            
            // 边框
            ctx.strokeStyle = '#d1d5db';
            ctx.lineWidth = 1;
            ctx.strokeRect(0, 0, 500, 350);
            
            // 标题
            ctx.fillStyle = '#2c5aa0';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('输入方案选择', 250, 40);
            
            // 方案列表
            const schemes = ['现代五笔', '拼音输入', '双拼方案', '自定义方案'];
            schemes.forEach((scheme, index) => {
                const y = 80 + index * 50;
                
                // 选项背景
                if (index === 0) {
                    ctx.fillStyle = '#e0f2fe';
                    ctx.fillRect(50, y - 20, 400, 40);
                }
                
                // 单选按钮
                ctx.strokeStyle = '#2c5aa0';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(80, y, 8, 0, 2 * Math.PI);
                ctx.stroke();
                
                if (index === 0) {
                    ctx.fillStyle = '#2c5aa0';
                    ctx.beginPath();
                    ctx.arc(80, y, 5, 0, 2 * Math.PI);
                    ctx.fill();
                }
                
                // 方案名称
                ctx.fillStyle = '#374151';
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(scheme, 110, y + 5);
            });
        }
        
        function drawVideoThumb(canvasId, title) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // 背景渐变
            ctx.fillStyle = createGradient(ctx, 400, 225, '#667eea', '#764ba2');
            ctx.fillRect(0, 0, 400, 225);
            
            // 播放按钮
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.beginPath();
            ctx.arc(200, 112, 30, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillStyle = '#2c5aa0';
            ctx.beginPath();
            ctx.moveTo(190, 95);
            ctx.lineTo(190, 129);
            ctx.lineTo(220, 112);
            ctx.closePath();
            ctx.fill();
            
            // 标题
            ctx.fillStyle = 'white';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(title, 200, 50);
            
            // 时长
            ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.fillRect(320, 185, 60, 25);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.fillText('05:30', 350, 200);
        }
        
        function generateAllImages() {
            drawLogo();
            drawInstallFiles();
            drawInstallWizard();
            drawInstallVerify();
            drawCandidateSelection();
            drawStatusbarDemo();
            drawSchemeSwitch();
            drawVideoThumb('video-thumb-1', 'KEG 安装教程');
            drawVideoThumb('video-thumb-2', 'KEG 高级功能');
        }
        
        function downloadImage(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 页面加载时生成所有图片
        window.addEventListener('load', generateAllImages);
    </script>
</body>
</html>
