/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 导航栏 */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #e5e5e5;
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-img {
    width: 40px;
    height: 40px;
    border-radius: 8px;
}

.logo-text {
    font-size: 24px;
    font-weight: bold;
    color: #2c5aa0;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: #2c5aa0;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #2c5aa0;
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero 区域 */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    overflow: hidden;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #fff, #f0f8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 20px;
    opacity: 0.9;
}

.hero-description {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 40px;
    opacity: 0.8;
}

.hero-buttons {
    display: flex;
    gap: 20px;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 15px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 16px;
}

.btn-primary {
    background: #ff6b6b;
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.btn-primary:hover {
    background: #ff5252;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #667eea;
    transform: translateY(-2px);
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

/* 产品特色 */
.features {
    padding: 100px 0;
    background: #f8fafc;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 20px;
    color: #2d3748;
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: #718096;
    margin-bottom: 60px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.feature-card {
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 15px;
    color: #2d3748;
}

.feature-card p {
    color: #718096;
    line-height: 1.6;
}

/* 下载区域 */
.download {
    padding: 100px 0;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.download-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.download-info h3 {
    font-size: 2rem;
    margin-bottom: 20px;
}

.download-features {
    list-style: none;
    margin: 30px 0;
}

.download-features li {
    padding: 10px 0;
    font-size: 1.1rem;
}

.download-buttons {
    text-align: center;
}

.btn-download {
    background: #ff6b6b;
    color: white;
    padding: 20px 40px;
    font-size: 1.2rem;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.btn-download:hover {
    background: #ff5252;
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 107, 107, 0.6);
}

.download-icon {
    font-size: 2rem;
}

.download-note {
    margin-top: 20px;
    opacity: 0.8;
}

/* 教程区域 */
.tutorial {
    padding: 100px 0;
    background: white;
}

.tutorial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.tutorial-card {
    background: #f8fafc;
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
}

.tutorial-card:hover {
    background: white;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.tutorial-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.tutorial-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #2d3748;
}

.tutorial-card p {
    color: #718096;
    margin-bottom: 25px;
    line-height: 1.6;
}

.btn-outline {
    background: transparent;
    color: #2c5aa0;
    border: 2px solid #2c5aa0;
    padding: 12px 25px;
}

.btn-outline:hover {
    background: #2c5aa0;
    color: white;
}

/* 社区区域 */
.community {
    padding: 100px 0;
    background: #f8fafc;
}

.community-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.community-card {
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.community-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.community-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.community-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #2d3748;
}

.community-card p {
    color: #718096;
    margin-bottom: 20px;
    line-height: 1.6;
}

.qq-groups {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 20px;
}

.qq-group {
    background: #2c5aa0;
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

/* FAQ 区域 */
.faq {
    padding: 100px 0;
    background: #f8fafc;
}

.faq-container {
    max-width: 800px;
    margin: 60px auto 0;
}

.faq-item {
    background: white;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item:hover {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.faq-question {
    padding: 25px 30px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    transition: background 0.3s ease;
}

.faq-question:hover {
    background: #f8fafc;
}

.faq-question h3 {
    margin: 0;
    font-size: 1.2rem;
    color: #2d3748;
    font-weight: 600;
}

.faq-toggle {
    font-size: 1.5rem;
    color: #2c5aa0;
    font-weight: bold;
    transition: transform 0.3s ease;
}

.faq-item.active .faq-toggle {
    transform: rotate(45deg);
}

.faq-answer {
    padding: 0 30px;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 0 30px 25px;
    max-height: 200px;
}

.faq-answer p {
    margin: 0;
    color: #718096;
    line-height: 1.6;
}

/* 关于我们 */
.about {
    padding: 100px 0;
    background: white;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    margin-top: 60px;
}

.about-text h3 {
    font-size: 2rem;
    margin-bottom: 20px;
    color: #2d3748;
}

.about-text p {
    color: #718096;
    margin-bottom: 20px;
    line-height: 1.8;
}

.about-stats {
    display: flex;
    gap: 40px;
    margin-top: 40px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: bold;
    color: #2c5aa0;
}

.stat-label {
    font-size: 0.9rem;
    color: #718096;
}

.about-image img {
    width: 100%;
    height: auto;
    border-radius: 15px;
}

/* 教程页面样式 */
.tutorial-hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.tutorial-hero h1 {
    font-size: 3rem;
    margin-bottom: 20px;
    font-weight: bold;
}

.tutorial-subtitle {
    font-size: 1.2rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.tutorial-nav {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.tutorial-nav-link {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.tutorial-nav-link:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.tutorial-section {
    padding: 80px 0;
}

.tutorial-section:nth-child(even) {
    background: #f8fafc;
}

.tutorial-content {
    margin-top: 60px;
}

.tutorial-step {
    display: flex;
    gap: 30px;
    margin-bottom: 50px;
    align-items: flex-start;
}

.step-number {
    background: #2c5aa0;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-content h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #2d3748;
}

.step-content p {
    color: #718096;
    margin-bottom: 20px;
    line-height: 1.6;
}

.step-list {
    list-style: none;
    padding: 0;
}

.step-list li {
    padding: 8px 0;
    color: #718096;
    position: relative;
    padding-left: 20px;
}

.step-list li::before {
    content: '•';
    color: #2c5aa0;
    position: absolute;
    left: 0;
    font-weight: bold;
}

.step-image {
    margin: 20px 0;
    text-align: center;
}

.step-image img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.step-note {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
    color: #856404;
}

.tutorial-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.tutorial-card h3 {
    color: #2d3748;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.shortcut-box {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    text-align: center;
    font-family: 'Courier New', monospace;
}

.shortcut-box kbd {
    background: #2c5aa0;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.9rem;
    margin: 0 5px;
}

.candidate-demo {
    text-align: center;
    margin: 20px 0;
}

.candidate-demo img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.feature-item {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.feature-item h3 {
    color: #2d3748;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.feature-item p {
    color: #718096;
    margin-bottom: 15px;
}

.feature-item ul {
    list-style: none;
    padding: 0;
}

.feature-item ul li {
    padding: 5px 0;
    color: #718096;
    position: relative;
    padding-left: 20px;
}

.feature-item ul li::before {
    content: '✓';
    color: #10b981;
    position: absolute;
    left: 0;
    font-weight: bold;
}

.how-to {
    background: #f0f9ff;
    border-left: 4px solid #2c5aa0;
    padding: 15px;
    margin-top: 20px;
    border-radius: 0 8px 8px 0;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.tip-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    text-align: center;
}

.tip-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.tip-card h3 {
    color: #2d3748;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.shortcut-list {
    text-align: left;
}

.shortcut-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e2e8f0;
}

.shortcut-item:last-child {
    border-bottom: none;
}

.shortcut {
    font-family: 'Courier New', monospace;
}

.shortcut kbd {
    background: #2c5aa0;
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.description {
    color: #718096;
}

.troubleshooting-content {
    margin-top: 40px;
}

.problem-item {
    background: white;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.problem-item h3 {
    color: #dc2626;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.solution h4 {
    color: #059669;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.solution ol {
    color: #718096;
    line-height: 1.6;
}

.solution ol li {
    margin-bottom: 8px;
}

.video-tutorial {
    padding: 80px 0;
    background: #f8fafc;
}

.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.video-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease;
}

.video-card:hover {
    transform: translateY(-5px);
}

.video-thumbnail {
    position: relative;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(44, 90, 160, 0.9);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-button:hover {
    background: rgba(44, 90, 160, 1);
    transform: translate(-50%, -50%) scale(1.1);
}

.video-card h3 {
    padding: 20px 20px 10px;
    color: #2d3748;
    margin: 0;
    font-size: 1.2rem;
}

.video-card p {
    padding: 0 20px;
    color: #718096;
    margin-bottom: 20px;
}

.video-card .btn {
    margin: 0 20px 20px;
}

.nav-menu a.active {
    color: #2c5aa0;
}

.nav-menu a.active::after {
    width: 100%;
}

/* 图片占位符样式 */
.step-image img[src*="install-step1.png"],
.candidate-demo img[src*="candidate-selection.png"],
.video-thumbnail img[src*="video-thumb"],
.hero-image img[src*="hero-screenshot.png"],
.about-image img[src*="about-illustration.png"],
.logo-img[src*="logo.png"] {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px dashed #2c5aa0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #2c5aa0;
    font-weight: bold;
    position: relative;
}

.step-image img[src*="install-step1.png"]::before {
    content: "安装步骤截图";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.candidate-demo img[src*="candidate-selection.png"]::before {
    content: "候选词界面";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.video-thumbnail img[src*="video-thumb"]::before {
    content: "视频缩略图";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.hero-image img[src*="hero-screenshot.png"]::before {
    content: "软件界面截图";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.about-image img[src*="about-illustration.png"]::before {
    content: "关于我们插图";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.logo-img[src*="logo.png"]::before {
    content: "KEG";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.2rem;
}

/* 页脚 */
.footer {
    background: #2d3748;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h4 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    color: #fff;
}

.footer-section p {
    color: #a0aec0;
    margin-bottom: 15px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: #a0aec0;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #fff;
}

.footer-social {
    display: flex;
    gap: 15px;
    margin-top: 15px;
}

.footer-social a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #4a5568;
    border-radius: 50%;
    text-decoration: none;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.footer-social a:hover {
    background: #2c5aa0;
    transform: translateY(-2px);
}

.btn-donate {
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-donate:hover {
    background: #ff5252;
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid #4a5568;
    padding-top: 20px;
    text-align: center;
    color: #a0aec0;
}

.footer-bottom p {
    margin-bottom: 5px;
}

/* 移动端导航菜单样式 */
.nav-menu.active {
    display: flex;
    position: fixed;
    top: 70px;
    left: 0;
    width: 100%;
    height: calc(100vh - 70px);
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: 50px;
    z-index: 999;
}

.nav-menu.active li {
    margin: 20px 0;
}

.nav-menu.active a {
    font-size: 1.2rem;
    padding: 10px 20px;
}

.nav-toggle.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.nav-toggle.active span:nth-child(2) {
    opacity: 0;
}

.nav-toggle.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 40px;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .download-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .tutorial-grid {
        grid-template-columns: 1fr;
    }
    
    .community-grid {
        grid-template-columns: 1fr;
    }
    
    .about-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .about-stats {
        justify-content: center;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .qq-groups {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero {
        padding: 100px 0 60px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .features,
    .download,
    .tutorial,
    .community,
    .about {
        padding: 60px 0;
    }

    .btn {
        padding: 12px 20px;
        font-size: 14px;
    }

    .btn-download {
        padding: 15px 30px;
        font-size: 1rem;
    }

    /* 教程页面移动端适配 */
    .tutorial-hero {
        padding: 100px 0 60px;
    }

    .tutorial-hero h1 {
        font-size: 2rem;
    }

    .tutorial-nav {
        flex-direction: column;
        align-items: center;
    }

    .tutorial-nav-link {
        margin-bottom: 10px;
        width: 200px;
        text-align: center;
    }

    .tutorial-step {
        flex-direction: column;
        text-align: center;
    }

    .step-number {
        margin: 0 auto 20px;
    }

    .feature-grid,
    .tips-grid,
    .video-grid {
        grid-template-columns: 1fr;
    }

    .shortcut-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .tutorial-section {
        padding: 60px 0;
    }

    .video-tutorial {
        padding: 60px 0;
    }
}
